import type React from "react"
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export type Theme = "light" | "dark" | "system"

export interface ComponentProps {
  className?: string
  children?: React.ReactNode
}

// NinjaTemplates Marketplace Types

export interface Template {
  id: string
  title: string
  titleEn?: string
  description: string
  descriptionEn?: string
  price: number
  originalPrice?: number
  rating: number
  reviewCount: number
  image: string
  images?: string[]
  category: string
  tags: string[]
  featured: boolean
  bestseller: boolean
  downloadCount?: number
  createdAt?: Date
  updatedAt?: Date
  author?: {
    name: string
    avatar?: string
  }
  demoUrl?: string
  downloadUrl?: string
  documentation?: string
  support?: boolean
}

export interface Category {
  id: string
  name: string
  nameEn?: string
  description: string
  descriptionEn?: string
  icon: string
  href: string
  color: string
  templateCount?: number
}

export interface Testimonial {
  id: string
  name: string
  nameEn?: string
  role: string
  roleEn?: string
  content: string
  contentEn?: string
  rating: number
  avatar: string
  createdAt?: Date
}

export interface HowItWorksStep {
  step: number
  title: string
  titleEn?: string
  description: string
  descriptionEn?: string
  icon: string
}

export interface NavigationItem {
  name: string
  nameEn?: string
  href: string
  active: boolean
  children?: NavigationItem[]
}

export interface SearchFilters {
  category?: string
  minPrice?: number
  maxPrice?: number
  rating?: number
  tags?: string[]
  sortBy?: 'newest' | 'oldest' | 'price-low' | 'price-high' | 'rating' | 'popular'
  featured?: boolean
  bestseller?: boolean
}

export interface CartItem {
  templateId: string
  template: Template
  quantity: number
  addedAt: Date
}

export interface Order {
  id: string
  userId: string
  items: CartItem[]
  total: number
  currency: string
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  paymentMethod: string
  createdAt: Date
  updatedAt: Date
}
