import React from 'react'
import Image from 'next/image'
import { Star, Quote, Users, Award, TrendingUp } from 'lucide-react'
import { TESTIMONIALS } from '@/lib/constants'

export default function SocialProofSection() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 arabic-heading">
            آراء العملاء
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto arabic-text mb-8">
            اكتشف ما يقوله عملاؤنا الراضون عن تجربتهم مع قوالبنا
          </p>

          {/* Overall Rating */}
          <div className="inline-flex items-center bg-saudi-50 border border-saudi-200 rounded-2xl px-8 py-4">
            <div className="flex items-center ml-6 rtl:ml-0 rtl:mr-6">
              <div className="text-4xl font-bold text-saudi-primary ml-2 rtl:ml-0 rtl:mr-2">4.7</div>
              <div className="flex flex-col">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${
                        i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <div className="text-sm text-gray-600 arabic-text">من 5 نجوم</div>
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">1,325</div>
              <div className="text-sm text-gray-600 arabic-text">مستخدم راضي</div>
            </div>
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {TESTIMONIALS.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-gray-50 rounded-2xl p-8 relative hover:shadow-lg transition-shadow duration-300"
            >
              {/* Quote Icon */}
              <div className="absolute -top-4 right-6 rtl:right-auto rtl:left-6">
                <div className="w-8 h-8 bg-saudi-primary rounded-full flex items-center justify-center">
                  <Quote className="w-4 h-4 text-white" />
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < testimonial.rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>

              {/* Content */}
              <p className="text-gray-700 arabic-text leading-relaxed mb-6">
                "{testimonial.content}"
              </p>

              {/* Author */}
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-full overflow-hidden ml-4 rtl:ml-0 rtl:mr-4">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <div className="font-bold text-gray-900 arabic-text">{testimonial.name}</div>
                  <div className="text-sm text-gray-600 arabic-text">{testimonial.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="bg-gradient-to-br from-saudi-50 to-white rounded-2xl border border-saudi-200 p-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div>
              <div className="w-16 h-16 mx-auto mb-4 bg-saudi-100 rounded-2xl flex items-center justify-center">
                <Users className="w-8 h-8 text-saudi-primary" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">10,000+</div>
              <div className="text-gray-600 arabic-text">مطور سعيد</div>
            </div>

            <div>
              <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-2xl flex items-center justify-center">
                <Award className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">500+</div>
              <div className="text-gray-600 arabic-text">قالب عالي الجودة</div>
            </div>

            <div>
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-2xl flex items-center justify-center">
                <TrendingUp className="w-8 h-8 text-green-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">98%</div>
              <div className="text-gray-600 arabic-text">معدل الرضا</div>
            </div>

            <div>
              <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-2xl flex items-center justify-center">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">24/7</div>
              <div className="text-gray-600 arabic-text">دعم فني</div>
            </div>
          </div>
        </div>

        {/* Featured Companies */}
        <div className="mt-16 text-center">
          <p className="text-gray-600 arabic-text mb-8">يثق بنا المطورون في أفضل الشركات</p>
          <div className="flex items-center justify-center space-x-12 rtl:space-x-reverse opacity-60">
            {/* Company Logos Placeholder */}
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-xs">شركة أ</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-xs">شركة ب</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-xs">شركة ج</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-xs">شركة د</span>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center bg-white border border-gray-200 rounded-full px-6 py-3 shadow-sm">
            <span className="text-gray-600 arabic-text ml-3 rtl:ml-0 rtl:mr-3">
              انضم إلى آلاف المطورين الراضين
            </span>
            <a 
              href="/templates" 
              className="text-saudi-primary font-medium hover:text-saudi-secondary transition-colors duration-200 arabic-text"
            >
              ابدأ الآن
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}
