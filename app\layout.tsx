import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Next.js Starter Template",
  description: "A production-ready Next.js starter template with TypeScript, TailwindCSS, and ShadCN UI",
  keywords: ["Next.js", "React", "TypeScript", "TailwindCSS", "ShadCN"],
  authors: [{ name: "Your Name" }],
  creator: "Your Name",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <div className="min-h-screen bg-background font-sans antialiased">
            <main className="relative flex min-h-screen flex-col">{children}</main>
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
