import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ThemeToggle } from "@/components/theme-toggle"

export default function HomePage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">Next.js Starter Template</h1>
          <p className="text-muted-foreground mt-2">Production-ready foundation for scalable web applications</p>
        </div>
        <ThemeToggle />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Next.js 15
              <Badge variant="secondary">Latest</Badge>
            </CardTitle>
            <CardDescription>App Router with Server Components and Server Actions</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full">Get Started</Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              TypeScript
              <Badge variant="outline">Type Safe</Badge>
            </CardTitle>
            <CardDescription>Full TypeScript support with strict configuration</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full bg-transparent">
              Learn More
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              ShadCN UI
              <Badge variant="default">Ready</Badge>
            </CardTitle>
            <CardDescription>Beautiful, accessible components built with Radix UI</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="secondary" className="w-full">
              Explore
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="mt-12 text-center">
        <p className="text-sm text-muted-foreground">
          Ready to build something amazing? Start by editing{" "}
          <code className="bg-muted px-1 py-0.5 rounded text-sm">app/page.tsx</code>
        </p>
      </div>
    </div>
  )
}
