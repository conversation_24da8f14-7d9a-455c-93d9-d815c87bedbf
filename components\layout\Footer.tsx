import React from 'react'
import Link from 'next/link'
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin, Youtube } from 'lucide-react'
import { SITE_CONFIG, ROUTES } from '@/lib/constants'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-saudi-800 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-saudi-gradient rounded-lg flex items-center justify-center ml-3 rtl:ml-0 rtl:mr-3">
                <span className="text-white font-bold text-xl">N</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold">{SITE_CONFIG.name}</span>
                <span className="text-saudi-200 arabic-text">{SITE_CONFIG.nameArabic}</span>
              </div>
            </div>
            <p className="text-saudi-200 arabic-text leading-relaxed mb-6">
              منصة رائدة في توفير أقوى القوالب البرمجية الجاهزة للمطورين العرب. 
              نساعدك في تطوير مشاريعك بسرعة واحترافية عالية.
            </p>
            
            {/* Social Media */}
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <a 
                href="#" 
                className="w-10 h-10 bg-saudi-700 hover:bg-saudi-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                aria-label="Facebook"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 bg-saudi-700 hover:bg-saudi-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                aria-label="Twitter"
              >
                <Twitter className="w-5 h-5" />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 bg-saudi-700 hover:bg-saudi-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                aria-label="Instagram"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 bg-saudi-700 hover:bg-saudi-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                aria-label="LinkedIn"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 bg-saudi-700 hover:bg-saudi-600 rounded-lg flex items-center justify-center transition-colors duration-200"
                aria-label="YouTube"
              >
                <Youtube className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-bold mb-6 arabic-heading">الخدمات</h3>
            <ul className="space-y-4">
              <li>
                <Link 
                  href={ROUTES.CATEGORIES.APPS} 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  قوالب التطبيقات
                </Link>
              </li>
              <li>
                <Link 
                  href={ROUTES.CATEGORIES.UI_UX} 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  واجهات المستخدم
                </Link>
              </li>
              <li>
                <Link 
                  href={ROUTES.CATEGORIES.WEB} 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  قوالب المواقع
                </Link>
              </li>
              <li>
                <Link 
                  href={ROUTES.CATEGORIES.OTHER} 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  قوالب أخرى
                </Link>
              </li>
              <li>
                <Link 
                  href="/custom-templates" 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  قوالب مخصصة
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-bold mb-6 arabic-heading">الدعم</h3>
            <ul className="space-y-4">
              <li>
                <Link 
                  href="/help" 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  مركز المساعدة
                </Link>
              </li>
              <li>
                <Link 
                  href="/documentation" 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  التوثيق
                </Link>
              </li>
              <li>
                <Link 
                  href="/tutorials" 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  دروس تعليمية
                </Link>
              </li>
              <li>
                <Link 
                  href="/faq" 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  الأسئلة الشائعة
                </Link>
              </li>
              <li>
                <Link 
                  href="/contact" 
                  className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
                >
                  اتصل بنا
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-bold mb-6 arabic-heading">تواصل معنا</h3>
            <div className="space-y-4">
              <div className="flex items-center">
                <Mail className="w-5 h-5 text-saudi-300 ml-3 rtl:ml-0 rtl:mr-3" />
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-saudi-200 hover:text-white transition-colors duration-200"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <Phone className="w-5 h-5 text-saudi-300 ml-3 rtl:ml-0 rtl:mr-3" />
                <a 
                  href="tel:+966123456789" 
                  className="text-saudi-200 hover:text-white transition-colors duration-200"
                >
                  +966 12 345 6789
                </a>
              </div>
              <div className="flex items-start">
                <MapPin className="w-5 h-5 text-saudi-300 ml-3 rtl:ml-0 rtl:mr-3 mt-1" />
                <span className="text-saudi-200 arabic-text">
                  الرياض، المملكة العربية السعودية
                </span>
              </div>
            </div>

            {/* Newsletter */}
            <div className="mt-8">
              <h4 className="font-bold mb-4 arabic-text">اشترك في النشرة الإخبارية</h4>
              <div className="flex">
                <input
                  type="email"
                  placeholder="بريدك الإلكتروني"
                  className="flex-1 px-4 py-2 bg-saudi-700 border border-saudi-600 rounded-r-lg rtl:rounded-r-none rtl:rounded-l-lg text-white placeholder-saudi-300 focus:outline-none focus:border-saudi-400"
                />
                <button className="px-6 py-2 bg-saudi-primary hover:bg-saudi-secondary rounded-l-lg rtl:rounded-l-none rtl:rounded-r-lg transition-colors duration-200 arabic-text">
                  اشترك
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-saudi-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            {/* Copyright */}
            <div className="text-saudi-200 arabic-text mb-4 md:mb-0">
              © {currentYear} {SITE_CONFIG.nameArabic}. جميع الحقوق محفوظة.
            </div>

            {/* Legal Links */}
            <div className="flex items-center space-x-6 rtl:space-x-reverse">
              <Link 
                href="/privacy" 
                className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
              >
                سياسة الخصوصية
              </Link>
              <Link 
                href="/terms" 
                className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
              >
                شروط الاستخدام
              </Link>
              <Link 
                href="/cookies" 
                className="text-saudi-200 hover:text-white transition-colors duration-200 arabic-text"
              >
                سياسة الكوكيز
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
