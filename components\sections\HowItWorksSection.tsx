import React from 'react'
import { Search, CheckCircle, Download, ArrowLeft } from 'lucide-react'
import { HOW_IT_WORKS_STEPS } from '@/lib/constants'

// Icon mapping
const iconMap = {
  search: Search,
  'check-circle': CheckCircle,
  download: Download,
}

export default function HowItWorksSection() {
  return (
    <section className="py-20 bg-gradient-to-br from-saudi-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 arabic-heading">
            كيف يعمل الموقع
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto arabic-text">
            ثلاث خطوات بسيطة للحصول على القالب المثالي لمشروعك
          </p>
        </div>

        {/* Steps */}
        <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
          {HOW_IT_WORKS_STEPS.map((step, index) => {
            const IconComponent = iconMap[step.icon as keyof typeof iconMap]
            const isLast = index === HOW_IT_WORKS_STEPS.length - 1

            return (
              <div key={step.step} className="relative">
                {/* Step Card */}
                <div className="bg-white rounded-2xl border border-gray-200 p-8 text-center hover:shadow-lg transition-shadow duration-300 relative z-10">
                  {/* Step Number */}
                  <div className="absolute -top-4 right-1/2 rtl:right-auto rtl:left-1/2 transform translate-x-1/2 rtl:-translate-x-1/2">
                    <div className="w-8 h-8 bg-saudi-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                      {step.step}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-saudi-100 to-saudi-200 rounded-2xl flex items-center justify-center">
                    <IconComponent className="w-10 h-10 text-saudi-primary" />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-gray-900 mb-4 arabic-heading">
                    {step.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-600 arabic-text leading-relaxed">
                    {step.description}
                  </p>
                </div>

                {/* Connecting Arrow (Desktop) */}
                {!isLast && (
                  <div className="hidden md:block absolute top-1/2 -left-6 rtl:-left-auto rtl:-right-6 transform -translate-y-1/2 z-0">
                    <div className="w-12 h-12 bg-saudi-100 rounded-full flex items-center justify-center">
                      <ArrowLeft className="w-6 h-6 text-saudi-primary rtl:rotate-180" />
                    </div>
                  </div>
                )}

                {/* Connecting Line (Mobile) */}
                {!isLast && (
                  <div className="md:hidden absolute -bottom-4 right-1/2 rtl:right-auto rtl:left-1/2 transform translate-x-1/2 rtl:-translate-x-1/2">
                    <div className="w-0.5 h-8 bg-saudi-200"></div>
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Additional Info */}
        <div className="mt-16 bg-white rounded-2xl border border-gray-200 p-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div>
              <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <h4 className="font-bold text-gray-900 mb-2 arabic-heading">دفع آمن</h4>
              <p className="text-sm text-gray-600 arabic-text">جميع المدفوعات محمية ومشفرة</p>
            </div>

            <div>
              <div className="w-12 h-12 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                <Download className="w-6 h-6 text-blue-600" />
              </div>
              <h4 className="font-bold text-gray-900 mb-2 arabic-heading">تحميل فوري</h4>
              <p className="text-sm text-gray-600 arabic-text">احصل على القالب فور إتمام الدفع</p>
            </div>

            <div>
              <div className="w-12 h-12 mx-auto mb-4 bg-purple-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <h4 className="font-bold text-gray-900 mb-2 arabic-heading">دعم فني</h4>
              <p className="text-sm text-gray-600 arabic-text">دعم فني مجاني لمدة 6 أشهر</p>
            </div>

            <div>
              <div className="w-12 h-12 mx-auto mb-4 bg-orange-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-bold text-gray-900 mb-2 arabic-heading">ضمان الجودة</h4>
              <p className="text-sm text-gray-600 arabic-text">ضمان استرداد المال خلال 30 يوم</p>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center bg-saudi-100 text-saudi-700 px-6 py-3 rounded-full">
            <span className="arabic-text ml-3 rtl:ml-0 rtl:mr-3">
              هل لديك أسئلة؟
            </span>
            <a 
              href="#contact" 
              className="text-saudi-primary font-medium hover:text-saudi-secondary transition-colors duration-200 arabic-text"
            >
              تواصل معنا
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}
