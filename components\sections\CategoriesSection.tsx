import React from 'react'
import Link from 'next/link'
import { Smartphone, Palette, Globe, Grid3X3, ArrowLeft, TrendingUp } from 'lucide-react'
import { TEMPLATE_CATEGORIES } from '@/lib/constants'

// Icon mapping
const iconMap = {
  smartphone: Smartphone,
  palette: Palette,
  globe: Globe,
  grid: Grid3X3,
}

export default function CategoriesSection() {
  return (
    <section id="categories" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 arabic-heading">
            فئات القوالب
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto arabic-text">
            اختر من بين مجموعة متنوعة من الفئات المتخصصة لتجد القالب المثالي لمشروعك
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {TEMPLATE_CATEGORIES.map((category, index) => {
            const IconComponent = iconMap[category.icon as keyof typeof iconMap]
            
            return (
              <Link
                key={category.id}
                href={category.href}
                className="group block"
              >
                <div className="bg-white rounded-xl border border-gray-200 p-8 text-center hover:shadow-lg hover:border-saudi-primary/20 transition-all duration-300 transform hover:-translate-y-1">
                  {/* Icon */}
                  <div className={`w-16 h-16 mx-auto mb-6 rounded-xl bg-gradient-to-br ${
                    index === 0 ? 'from-saudi-primary to-saudi-secondary' :
                    index === 1 ? 'from-blue-500 to-blue-600' :
                    index === 2 ? 'from-purple-500 to-purple-600' :
                    'from-orange-500 to-orange-600'
                  } flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>

                  {/* Category Name */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3 arabic-heading group-hover:text-saudi-primary transition-colors duration-300">
                    {category.name}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-600 mb-6 arabic-text leading-relaxed">
                    {category.description}
                  </p>

                  {/* Stats */}
                  <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse mb-6">
                    <div className="flex items-center text-sm text-gray-500">
                      <TrendingUp className="w-4 h-4 ml-1 rtl:ml-0 rtl:mr-1" />
                      <span className="arabic-text">
                        {index === 0 ? '120+ قالب' :
                         index === 1 ? '85+ قالب' :
                         index === 2 ? '95+ قالب' :
                         '60+ قالب'}
                      </span>
                    </div>
                  </div>

                  {/* CTA */}
                  <div className="flex items-center justify-center text-saudi-primary font-medium group-hover:text-saudi-secondary transition-colors duration-300">
                    <span className="arabic-text">استكشف القوالب</span>
                    <ArrowLeft className="w-4 h-4 mr-2 rtl:mr-0 rtl:ml-2 rtl:rotate-180 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform duration-300" />
                  </div>
                </div>
              </Link>
            )
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12">
          <div className="inline-flex items-center bg-white border border-gray-200 rounded-full px-6 py-3 shadow-sm">
            <span className="text-gray-600 arabic-text ml-3 rtl:ml-0 rtl:mr-3">
              لا تجد ما تبحث عنه؟
            </span>
            <Link 
              href="/contact" 
              className="text-saudi-primary font-medium hover:text-saudi-secondary transition-colors duration-200 arabic-text"
            >
              اطلب قالب مخصص
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
