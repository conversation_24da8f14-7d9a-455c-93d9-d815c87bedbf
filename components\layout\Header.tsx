"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { Search, Menu, X, User, Globe } from 'lucide-react'
import { SITE_CONFIG, NAVIGATION_ITEMS } from '@/lib/constants'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        {/* Main Header */}
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-saudi-gradient rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">N</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-gray-900">{SITE_CONFIG.name}</span>
                <span className="text-sm text-saudi-primary arabic-text">{SITE_CONFIG.nameArabic}</span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {NAVIGATION_ITEMS.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`text-sm font-medium transition-colors duration-200 arabic-text ${
                  item.active
                    ? 'text-saudi-primary border-b-2 border-saudi-primary pb-1'
                    : 'text-gray-700 hover:text-saudi-primary'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex items-center flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute right-3 rtl:right-auto rtl:left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="ابحث عن القوالب..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pr-10 rtl:pr-3 rtl:pl-10 arabic-text text-right rtl:text-right"
              />
            </div>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Language Switcher */}
            <Button variant="ghost" size="sm" className="hidden md:flex items-center space-x-2 rtl:space-x-reverse">
              <Globe className="w-4 h-4" />
              <span className="text-sm">العربية</span>
            </Button>

            {/* Auth Buttons */}
            <div className="hidden md:flex items-center space-x-3 rtl:space-x-reverse">
              <Button variant="ghost" size="sm" className="arabic-text">
                تسجيل الدخول
              </Button>
              <Button size="sm" className="btn-saudi-primary arabic-text">
                إنشاء حساب
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Search Bar */}
        <div className="md:hidden pb-4">
          <div className="relative">
            <Search className="absolute right-3 rtl:right-auto rtl:left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="ابحث عن القوالب..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pr-10 rtl:pr-3 rtl:pl-10 arabic-text text-right rtl:text-right"
            />
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200">
          <div className="container mx-auto px-4 py-4">
            {/* Navigation Links */}
            <nav className="space-y-4 mb-6">
              {NAVIGATION_ITEMS.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`block text-base font-medium transition-colors duration-200 arabic-text ${
                    item.active
                      ? 'text-saudi-primary'
                      : 'text-gray-700 hover:text-saudi-primary'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </nav>

            {/* Mobile Actions */}
            <div className="space-y-3 pt-4 border-t border-gray-200">
              <Button variant="ghost" className="w-full justify-start arabic-text">
                <Globe className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
                العربية
              </Button>
              <Button variant="ghost" className="w-full justify-start arabic-text">
                <User className="w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2" />
                تسجيل الدخول
              </Button>
              <Button className="w-full btn-saudi-primary arabic-text">
                إنشاء حساب
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
