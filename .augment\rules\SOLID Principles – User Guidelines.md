---
type: "always_apply"
---

Ensure all software design suggestions, code generation, and architectural recommendations adhere to SOLID principles in OOP.

1. Single Responsibility Principle (SRP)
✅ Every class or module should have only one reason to change. When generating or refactoring code, avoid combining multiple responsibilities in a single class. Separate concerns clearly.

Example Instruction:

When creating a service that processes invoices, handle calculation in one class and sending emails in another.

2. Open/Closed Principle (OCP)
✅ Systems should be open for extension but closed for modification. Favor inheritance, composition, or design patterns like Strategy or Decorator to add new features without changing existing code.

Example Instruction:

If new payment methods are added, design the system to plug in new strategies without editing core logic.

3. Liskov Substitution Principle (LSP)
✅ Subtypes must be fully substitutable for their base types without breaking the application. Avoid overriding methods in a way that violates expected behavior.

Example Instruction:

Don’t override a fly() method in a subclass of Bird with an exception in a Penguin. Instead, restructure class hierarchy.

4. Interface Segregation Principle (ISP)
✅ Prefer multiple small, specific interfaces over a single large one. Ensure no class is forced to implement methods it does not use.

Example Instruction:

Separate IPrinter into IScanner, IPrint, and IFax to allow clients to only implement what they need.

5. Dependency Inversion Principle (DIP)
✅ High-level modules should not depend on low-level modules. Both should depend on abstractions. Always inject dependencies using interfaces or abstractions, not concrete classes.

Example Instruction:

When generating code for services, inject repositories or database layers via interfaces to allow easy mocking and swapping.

🛠 Additional Guidelines
When asked to generate object-oriented code, apply SOLID principles where applicable by default.

If code violates a SOLID principle, add a comment suggesting improvement or refactoring.

Favor clean architecture and separation of concerns in all designs.

Use dependency injection patterns wherever applicable.

