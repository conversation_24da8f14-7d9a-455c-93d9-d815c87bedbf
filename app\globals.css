@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-white text-gray-900 font-arabic;
    font-feature-settings: "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html[dir="rtl"] {
    direction: rtl;
  }

  html[dir="rtl"] body {
    text-align: right;
  }
}

@layer components {
  /* Saudi Government Design System Components */
  .btn-saudi-primary {
    @apply bg-saudi-primary hover:bg-saudi-secondary text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200;
  }

  .btn-saudi-secondary {
    @apply bg-white border-2 border-saudi-primary text-saudi-primary hover:bg-saudi-primary hover:text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200;
  }

  .card-saudi {
    @apply bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  .bg-saudi-gradient {
    background: linear-gradient(135deg, #1B7B3A 0%, #2D8F47 100%);
  }

  /* RTL-specific utilities */
  .rtl\:text-right {
    text-align: right;
  }

  .rtl\:text-left {
    text-align: left;
  }

  .rtl\:mr-auto {
    margin-right: auto;
  }

  .rtl\:ml-auto {
    margin-left: auto;
  }

  /* Arabic typography improvements */
  .arabic-text {
    font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
    line-height: 1.8;
    letter-spacing: 0.01em;
  }

  .arabic-heading {
    font-family: 'Cairo', 'Tajawal', system-ui, sans-serif;
    font-weight: 700;
    line-height: 1.4;
  }
}
