// Site Configuration for NinjaTemplates Arabic Marketplace
import { LucideIcon } from 'lucide-react'

// TopBar Announcement Configuration
export interface SiteAnnouncement {
  type: 'promo' | 'info' | null
  message: string
  messageEn?: string
  link?: string
  linkText?: string
  linkTextEn?: string
  isActive: boolean
  emoji?: string
}

// Navigation Item Interface
export interface NavigationItem {
  name: string
  nameEn?: string
  href: string
  icon?: string
  hasDropdown?: boolean
  dropdownItems?: DropdownItem[]
}

export interface DropdownItem {
  name: string
  nameEn?: string
  href: string
  icon?: string
  description?: string
}

// Current Site Announcement Configuration
export const SITE_ANNOUNCEMENT: SiteAnnouncement = {
  type: 'promo', // Change to 'info' or null to switch modes
  message: 'احصل على خصم 30٪ على جميع قوالب React حتى نهاية الأسبوع!',
  messageEn: 'Get 30% off on all React templates until end of week!',
  link: '/templates?category=react&discount=30',
  linkText: 'عرض التفاصيل',
  linkTextEn: 'View Details',
  isActive: true,
  emoji: '🔥'
}

// Alternative Info Announcement (switch SITE_ANNOUNCEMENT.type to 'info' to use)
export const INFO_ANNOUNCEMENT: SiteAnnouncement = {
  type: 'info',
  message: 'مرحباً بكم في منصة نينجا تمبليتس - أقوى القوالب البرمجية',
  messageEn: 'Welcome to NinjaTemplates - The most powerful programming templates',
  isActive: true,
  emoji: '🌐'
}

// Main Navigation Configuration
export const MAIN_NAVIGATION: NavigationItem[] = [
  {
    name: 'القوالب',
    nameEn: 'Templates',
    href: '/templates',
    icon: 'shopping-bag',
    hasDropdown: true,
    dropdownItems: [
      { 
        name: 'تطبيقات', 
        nameEn: 'Apps',
        href: '/templates/apps', 
        icon: 'smartphone',
        description: 'قوالب تطبيقات الجوال والويب'
      },
      { 
        name: 'واجهات المستخدم', 
        nameEn: 'UI/UX',
        href: '/templates/ui-ux', 
        icon: 'palette',
        description: 'تصاميم UI/UX احترافية'
      },
      { 
        name: 'مواقع ويب', 
        nameEn: 'Websites',
        href: '/templates/web', 
        icon: 'globe',
        description: 'مواقع ويب جاهزة للاستخدام'
      },
      { 
        name: 'أخرى', 
        nameEn: 'Other',
        href: '/templates/other', 
        icon: 'grid-3x3',
        description: 'قوالب متنوعة أخرى'
      }
    ]
  },
  { 
    name: 'المقالات', 
    nameEn: 'Articles', 
    href: '/articles', 
    icon: 'newspaper' 
  },
  { 
    name: 'الأسئلة الشائعة', 
    nameEn: 'FAQ', 
    href: '/faq', 
    icon: 'help-circle' 
  },
  { 
    name: 'الدعم الفني', 
    nameEn: 'Support', 
    href: '/support', 
    icon: 'headphones' 
  }
]

// User Authentication Menu Items
export const AUTH_MENU_ITEMS = {
  login: {
    name: 'تسجيل الدخول',
    nameEn: 'Login',
    href: '/auth/login'
  },
  register: {
    name: 'إنشاء حساب',
    nameEn: 'Register',
    href: '/auth/register'
  },
  profile: {
    name: 'الملف الشخصي',
    nameEn: 'Profile',
    href: '/profile'
  },
  orders: {
    name: 'طلباتي',
    nameEn: 'My Orders',
    href: '/orders'
  },
  favorites: {
    name: 'المفضلة',
    nameEn: 'Favorites',
    href: '/favorites'
  },
  settings: {
    name: 'الإعدادات',
    nameEn: 'Settings',
    href: '/settings'
  },
  logout: {
    name: 'تسجيل الخروج',
    nameEn: 'Logout',
    href: '/auth/logout'
  }
}

// TopBar Dismissal Configuration
export const TOPBAR_CONFIG = {
  dismissalKey: 'ninja-topbar-dismissed',
  dismissalDuration: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  animationDuration: 300 // Animation duration in milliseconds
}

// Language Configuration
export const LANGUAGE_CONFIG = {
  default: 'ar',
  supported: ['ar', 'en'],
  labels: {
    ar: 'العربية',
    en: 'English'
  }
}

// Search Configuration
export const SEARCH_CONFIG = {
  placeholder: 'ابحث عن القوالب، المقالات، والمزيد...',
  placeholderEn: 'Search templates, articles, and more...',
  recentSearches: 'عمليات البحث الأخيرة',
  recentSearchesEn: 'Recent Searches',
  popularSearches: [
    'React',
    'Next.js',
    'تطبيقات الجوال',
    'لوحة تحكم',
    'متجر إلكتروني',
    'واجهات مستخدم'
  ]
}

// Utility function to get current announcement
export const getCurrentAnnouncement = (): SiteAnnouncement | null => {
  if (!SITE_ANNOUNCEMENT.isActive) return null
  return SITE_ANNOUNCEMENT
}

// Utility function to check if topbar should be shown
export const shouldShowTopBar = (): boolean => {
  if (typeof window === 'undefined') return true // SSR
  
  const announcement = getCurrentAnnouncement()
  if (!announcement) return false
  
  const dismissedData = localStorage.getItem(TOPBAR_CONFIG.dismissalKey)
  if (!dismissedData) return true
  
  try {
    const { timestamp } = JSON.parse(dismissedData)
    const now = Date.now()
    const timeDiff = now - timestamp
    
    return timeDiff > TOPBAR_CONFIG.dismissalDuration
  } catch {
    return true
  }
}

// Utility function to dismiss topbar
export const dismissTopBar = (): void => {
  if (typeof window === 'undefined') return
  
  const dismissalData = {
    timestamp: Date.now(),
    type: SITE_ANNOUNCEMENT.type
  }
  
  localStorage.setItem(TOPBAR_CONFIG.dismissalKey, JSON.stringify(dismissalData))
}
