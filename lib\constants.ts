export const APP_CONFIG = {
  name: "Next.js Starter Template",
  description: "A production-ready Next.js starter template",
  url: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
  version: "1.0.0",
} as const

export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_URL || "/api",
  timeout: 10000,
} as const

export const ROUTES = {
  home: "/",
  about: "/about",
  contact: "/contact",
} as const
