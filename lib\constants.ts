// NinjaTemplates Marketplace Constants
export const SITE_CONFIG = {
  name: "NinjaTemplates",
  nameArabic: "نينجا تمبليتس",
  description: "أقوى القوالب البرمجية الجاهزة للمطورين العرب",
  url: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
  currency: "SAR",
  currencySymbol: "ر.س",
  version: "1.0.0",
} as const

export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_URL || "/api",
  timeout: 10000,
} as const

export const ROUTES = {
  HOME: '/',
  TEMPLATES: '/templates',
  CATEGORIES: {
    APPS: '/templates/apps',
    UI_UX: '/templates/ui-ux',
    WEB: '/templates/web',
    OTHER: '/templates/other',
  },
  TEMPLATE_DETAIL: '/templates/[slug]',
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
  }
} as const

// Template Categories (Fixed - Admin Only Upload)
export const TEMPLATE_CATEGORIES = [
  {
    id: 'apps',
    name: 'تطبيقات',
    nameEn: 'Apps',
    description: 'قوالب تطبيقات الجوال والويب',
    descriptionEn: 'Mobile and Web App Templates',
    icon: 'smartphone',
    href: ROUTES.CATEGORIES.APPS,
    color: 'saudi-primary'
  },
  {
    id: 'ui-ux',
    name: 'واجهات المستخدم',
    nameEn: 'UI/UX',
    description: 'تصاميم UI/UX احترافية',
    descriptionEn: 'Professional UI/UX Designs',
    icon: 'palette',
    href: ROUTES.CATEGORIES.UI_UX,
    color: 'saudi-secondary'
  },
  {
    id: 'web',
    name: 'قوالب الويب',
    nameEn: 'Web Templates',
    description: 'مواقع ويب جاهزة للاستخدام',
    descriptionEn: 'Ready-to-use Websites',
    icon: 'globe',
    href: ROUTES.CATEGORIES.WEB,
    color: 'saudi-accent'
  },
  {
    id: 'other',
    name: 'أخرى',
    nameEn: 'Other',
    description: 'قوالب متنوعة أخرى',
    descriptionEn: 'Various Other Templates',
    icon: 'grid',
    href: ROUTES.CATEGORIES.OTHER,
    color: 'saudi-primary'
  }
] as const

// Sample Featured Templates Data
export const FEATURED_TEMPLATES = [
  {
    id: '1',
    title: 'تطبيق التجارة الإلكترونية',
    titleEn: 'E-commerce App',
    description: 'تطبيق متكامل للتجارة الإلكترونية مع لوحة تحكم',
    price: 299,
    originalPrice: 399,
    rating: 4.8,
    reviewCount: 124,
    image: '/placeholder.jpg',
    category: 'apps',
    tags: ['React Native', 'Node.js', 'MongoDB'],
    featured: true,
    bestseller: true
  },
  {
    id: '2',
    title: 'واجهة لوحة التحكم الإدارية',
    titleEn: 'Admin Dashboard UI',
    description: 'واجهة حديثة ومتجاوبة للوحات الإدارية',
    price: 199,
    originalPrice: 249,
    rating: 4.9,
    reviewCount: 89,
    image: '/placeholder.jpg',
    category: 'ui-ux',
    tags: ['React', 'TailwindCSS', 'TypeScript'],
    featured: true,
    bestseller: false
  },
  {
    id: '3',
    title: 'موقع شركة تقنية',
    titleEn: 'Tech Company Website',
    description: 'موقع ويب احترافي للشركات التقنية',
    price: 149,
    originalPrice: 199,
    rating: 4.7,
    reviewCount: 156,
    image: '/placeholder.jpg',
    category: 'web',
    tags: ['Next.js', 'TailwindCSS', 'Framer Motion'],
    featured: true,
    bestseller: false
  },
  {
    id: '4',
    title: 'نظام إدارة المحتوى',
    titleEn: 'Content Management System',
    description: 'نظام متكامل لإدارة المحتوى والمقالات',
    price: 349,
    originalPrice: 449,
    rating: 4.6,
    reviewCount: 78,
    image: '/placeholder.jpg',
    category: 'other',
    tags: ['Laravel', 'Vue.js', 'MySQL'],
    featured: true,
    bestseller: false
  }
] as const

// How It Works Steps
export const HOW_IT_WORKS_STEPS = [
  {
    step: 1,
    title: 'تصفح القوالب',
    titleEn: 'Browse Templates',
    description: 'استكشف مجموعتنا المتنوعة من القوالب عالية الجودة',
    descriptionEn: 'Explore our diverse collection of high-quality templates',
    icon: 'search'
  },
  {
    step: 2,
    title: 'اختر القالب المناسب',
    titleEn: 'Choose the Right Template',
    description: 'اختر القالب الذي يناسب مشروعك ومتطلباتك',
    descriptionEn: 'Select the template that fits your project and requirements',
    icon: 'check-circle'
  },
  {
    step: 3,
    title: 'قم بالدفع واحصل عليه فورًا',
    titleEn: 'Pay and Download Instantly',
    description: 'ادفع بأمان واحصل على القالب فورًا مع الدعم الفني',
    descriptionEn: 'Pay securely and get instant access with technical support',
    icon: 'download'
  }
] as const

// Customer Testimonials
export const TESTIMONIALS = [
  {
    id: '1',
    name: 'أحمد محمد',
    nameEn: 'Ahmed Mohammed',
    role: 'مطور تطبيقات',
    roleEn: 'App Developer',
    content: 'قوالب رائعة وجودة عالية، وفرت علي الكثير من الوقت والجهد في تطوير مشاريعي',
    contentEn: 'Amazing templates with high quality, saved me a lot of time and effort in developing my projects',
    rating: 5,
    avatar: '/placeholder-user.jpg'
  },
  {
    id: '2',
    name: 'فاطمة العلي',
    nameEn: 'Fatima Al-Ali',
    role: 'مصممة واجهات',
    roleEn: 'UI Designer',
    content: 'التصاميم احترافية ومتجاوبة، والدعم الفني ممتاز ويساعد في حل أي مشكلة',
    contentEn: 'Professional and responsive designs, excellent technical support that helps solve any issue',
    rating: 5,
    avatar: '/placeholder-user.jpg'
  },
  {
    id: '3',
    name: 'خالد السعد',
    nameEn: 'Khalid Al-Saad',
    role: 'مطور ويب',
    roleEn: 'Web Developer',
    content: 'أفضل موقع للحصول على قوالب جاهزة، الأسعار معقولة والجودة ممتازة',
    contentEn: 'Best site to get ready-made templates, reasonable prices and excellent quality',
    rating: 4,
    avatar: '/placeholder-user.jpg'
  }
] as const

// Navigation Menu Items
export const NAVIGATION_ITEMS = [
  {
    name: 'الرئيسية',
    nameEn: 'Home',
    href: ROUTES.HOME,
    active: true
  },
  {
    name: 'التطبيقات',
    nameEn: 'Apps',
    href: ROUTES.CATEGORIES.APPS,
    active: false
  },
  {
    name: 'واجهات المستخدم',
    nameEn: 'UI/UX',
    href: ROUTES.CATEGORIES.UI_UX,
    active: false
  },
  {
    name: 'قوالب الويب',
    nameEn: 'Web Templates',
    href: ROUTES.CATEGORIES.WEB,
    active: false
  },
  {
    name: 'أخرى',
    nameEn: 'Other',
    href: ROUTES.CATEGORIES.OTHER,
    active: false
  }
] as const
