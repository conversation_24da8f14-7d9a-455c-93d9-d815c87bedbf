import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface SampleComponentProps {
  title?: string
  description?: string
}

export function SampleComponent({
  title = "Sample Component",
  description = "This is a sample component to validate ShadCN UI integration",
}: SampleComponentProps) {
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2">
          <Button>Primary</Button>
          <Button variant="outline">Secondary</Button>
        </div>
      </CardContent>
    </Card>
  )
}
